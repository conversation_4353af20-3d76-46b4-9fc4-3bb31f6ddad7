# 权限管理系统需求文档

## 1. 功能概述
新增权限管理模块，包含角色管理和用户(组)管理两大核心功能。

## 2. 详细需求说明

### 2.1 权限管理菜单
- 新增顶级菜单项"权限管理"
- 包含两个子模块：
    - 角色管理
    - 用户(组)管理

### 2.2 角色管理功能

#### 2.2.1 默认角色
| 角色类型 | 权限说明 | 特殊规则 |
|---------|----------|----------|
| 系统管理员(detpAdmin) | 查看所有菜单，操作不受限 | 不可删除，可授权/解除其他管理员 |
| 访客 | 仅可访问首页 | - |
| 平台用户 | 使用非权限管理菜单功能 | - |

#### 2.2.2 自定义角色
- **创建规则**：
    - 最大数量：1000个
    - 角色名称：≤20个字符
    - 必须分配菜单权限
- **管理功能**：
    - 支持单个/批量删除
    - 支持修改菜单权限
    - 支持查看关联用户/用户组

### 2.3 用户(组)管理功能

#### 2.3.1 用户管理
- **添加用户**：
    - 通过UAC接口验证用户身份（中兴/子公司员工）
    - 每个用户最多分配10个角色
- **权限管理**：
    - 可查看用户权限并集（包含直接分配和用户组继承的权限）
- **删除规则**：
    - 同步移除所属用户组关系

#### 2.3.2 用户组管理
- **创建规则**：
    - 最大数量：1000个
    - 每个组最多10个角色
- **成员管理**：
    - 从现有用户中选择成员
    - 成员自动继承用户组权限
- **修改功能**：
    - 支持增删成员
    - 支持调整角色分配
- **删除规则**：
    - 级联删除组内用户关系

## 3. 界面要求
1. **角色管理界面**需包含：
    - 默认角色特殊标识
    - 角色权限矩阵展示
    - 批量操作功能

2. **用户管理界面**需包含：
    - 用户-角色关联关系可视化
    - 权限继承路径展示

3. **用户组管理界面**需包含：
    - 组成员列表
    - 组权限概览

## 4. 非功能性需求
1. **性能要求**：
    - 100并发下响应时间<2秒
    - 万级用户数据加载时间<3秒

2. **安全要求**：
    - 所有权限变更操作记录审计日志
    - 敏感操作需二次确认

3. **限制条件**：
    - 默认角色不可删除/重命名
    - 用户组角色分配不得超过10个